<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <category android:name="android.intent.category.BROWSABLE" />
      <data android:scheme="https" />
    </intent>
  </queries>
  <application android:name=".MainApplication" android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher" android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="true" android:theme="@style/AppTheme" android:supportsRtl="true">
    <meta-data android:name="com.google.firebase.messaging.default_notification_color"
      android:resource="@color/notification_icon_color" tools:replace="android:resource" />
    <meta-data android:name="expo.modules.notifications.default_notification_color"
      android:resource="@color/notification_icon_color" />
    <meta-data android:name="expo.modules.updates.ENABLED" android:value="true" />
    <meta-data android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
      android:value="@string/expo_runtime_version" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
      android:value="ALWAYS" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS" android:value="0" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATE_URL"
      android:value="https://u.expo.dev/5c37501a-d3f1-49d2-bf38-28446fc1b0bb" />
    <activity android:name=".MainActivity"
      android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
      android:launchMode="singleTask" android:windowSoftInputMode="adjustResize"
      android:theme="@style/Theme.App.SplashScreen" android:exported="true"
      android:screenOrientation="unspecified">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="com.edunovaasia.paragon" />
        <data android:scheme="exp+edu-sis-paragan" />
      </intent-filter>
    </activity>
  </application>
</manifest>