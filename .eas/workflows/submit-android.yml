name: Submit Android app

on:
  workflow_dispatch:
    inputs:
      build_id:
        description: 'Build ID to submit (optional)'
        required: false
        type: string
      profile:
        description: 'Submit profile'
        required: true
        default: 'production'
        type: choice
        options:
          - production

jobs:
  submit_android:
    name: Submit Android
    type: submit
    params:
      platform: android
      profile: ${{ inputs.profile || 'production' }}
      build_id: ${{ inputs.build_id }}
      
  notify_submission:
    name: Notify Submission
    type: function
    needs: submit_android
    params:
      script: |
        echo "Android app submitted successfully!"
        echo "Submission ID: ${{ needs.submit_android.outputs.submission_id }}"
